.wishlist-container {
  width: 100%;
}

.wishlist-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.clear-btn {
  background: none;
  color: red;
  border: none;
  cursor: pointer;
}

.clear-btn:hover {
  background: none;
}

.wishlist-heading {
  font-size: 16px;
  margin-bottom: 12px;
}

.items-container {
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  gap: 20px;
}
.wishlist-items {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.wishlist-checkbox {
  flex-shrink: 0;
}

.wishlist-image img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
  flex-shrink: 0;
}

.wishlist-details {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.wishlist-name {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.wishlist-price {
  font-weight: bold;
  font-size: 14px;
  margin-top: 4px;
  color: #000;
}

.wishlist-delete {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #888;
  padding: 4px;
  flex-shrink: 0;
}

.wishlist-delete:hover {
  color: #ff4444;
}

.delete-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 18px;
  cursor: pointer;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-box {
  background-color: #fff;
  padding: 24px;
  border-radius: 8px;
  width: 90%;
  min-height: 150px;
  max-width: 400px;
  text-align: center;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.modal-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.modal-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background-color: #ddd;
  color: #333;
}

.modal-btn.danger {
  background-color: #e74c3c;
  color: #fff;
}

.hidden {
  display: none;
}

/* Enhanced wishlist item styles */
.wishlist-items {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  padding: 16px;
}

.wishlist-items:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.wishlist-brand {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
}

.wishlist-rating {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  margin-top: 4px;
}

.wishlist-rating .rating-stars {
  color: #E6B120;
}

.wishlist-rating .rating-text {
  color: #495057;
  font-weight: 500;
}

.wishlist-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.wishlist-view {
  background: none;
  border: 2px solid #E6B120;
  color: #E6B120;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.wishlist-view:hover {
  background-color: #E6B120;
  color: white;
}

.wishlist-delete {
  border: 2px solid #dc3545;
  color: #dc3545;
  font-size: 12px;
}

.wishlist-delete:hover {
  background-color: #dc3545;
  color: white;
}
